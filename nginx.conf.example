server {
    listen 80;
    server_name jiaohu.jingyuncenter.com;

    # 重定向 HTTP 到 HTTPS
    return 301 https://$host$request_uri;
}

server {
    listen 443 ssl;
    server_name jiaohu.jingyuncenter.com;

    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL 参数
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305';
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:10m;
    
    # 根目录配置
    root /www/wwwroot/szr.jingyuncenter.com;
    index index.html;
    
    # 日志配置
    access_log /var/log/nginx/jiaohu.access.log;
    error_log /var/log/nginx/jiaohu.error.log;

    # 处理静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000";
        try_files $uri =404;
    }
    
    # 代理 API 请求到后端服务器
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 处理上传文件的请求
    location /uploads/ {
        alias /www/wwwroot/szr.jingyuncenter.com/uploads/;
        try_files $uri =404;
    }
    
    # 处理所有其他请求，重定向到 index.html 以支持 React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
} 