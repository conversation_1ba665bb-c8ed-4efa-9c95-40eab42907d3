import axios from 'axios';
import { DigitalHumanConfig } from '../types/config';
import { API_BASE_URL, STATIC_BASE_URL } from '../config/constants';

export class ConfigService {
  private static instance: ConfigService;
  private config: DigitalHumanConfig | null = null;

  private constructor() {}

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  private processResourceUrl(url: string): string {
    if (!url) return '';
    if (url.startsWith('http')) return url;
    if (url.startsWith('/uploads/')) {
      return `${STATIC_BASE_URL}${url}`;
    }
    return `${API_BASE_URL}${url}`;
  }

  private convertToRelativeUrl(url: string): string {
    if (!url) return '';
    if (!url.startsWith('http')) return url;
    
    // 处理静态资源URL
    if (url.startsWith(STATIC_BASE_URL)) {
      return url.replace(STATIC_BASE_URL, '');
    }
    
    // 处理API URL
    if (url.startsWith(API_BASE_URL)) {
      return url.replace(API_BASE_URL, '');
    }
    
    // 如果是其他完整URL，保持原样
    return url;
  }

  public async getConfig(): Promise<DigitalHumanConfig> {
    try {
      const response = await axios.get<DigitalHumanConfig>(`${API_BASE_URL}/config`);
      const config = response.data;

      // 处理所有资源URL
      this.config = {
        ...config,
        customParams: config.customParams || [],
        backgroundImageUrl: this.processResourceUrl(config.backgroundImageUrl),
        defaultBackgroundUrl: this.processResourceUrl(config.defaultBackgroundUrl),
        keywordBackgrounds: config.keywordBackgrounds?.map(bg => ({
          ...bg,
          url: this.processResourceUrl(bg.url)
        })) || []
      };

      return this.config;
    } catch (error) {
      console.error('获取配置失败:', error);
      throw new Error('获取配置失败');
    }
  }

  public async saveConfig(config: DigitalHumanConfig): Promise<void> {
    try {
      // 在保存前处理所有URL
      const processedConfig = {
        ...config,
        customParams: config.customParams || [],
        backgroundImageUrl: this.convertToRelativeUrl(config.backgroundImageUrl),
        defaultBackgroundUrl: this.convertToRelativeUrl(config.defaultBackgroundUrl),
        keywordBackgrounds: config.keywordBackgrounds?.map(bg => ({
          ...bg,
          url: this.convertToRelativeUrl(bg.url)
        })) || []
      };

      await axios.post(`${API_BASE_URL}/config`, processedConfig);
      this.config = config;
    } catch (error) {
      console.error('保存配置失败:', error);
      throw new Error('保存配置失败');
    }
  }
} 