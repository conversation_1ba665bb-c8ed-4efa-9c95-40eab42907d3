import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

export interface FileMetadata {
  id: string;
  url: string;
  type: 'image' | 'video';
  name: string;
  uploadTime: string;
}

export class FileService {
  private static instance: FileService;
  private uploadedFiles: FileMetadata[] = [];
  private upload: multer.Multer;
  private readonly metadataFile: string;

  private constructor() {
    this.metadataFile = path.join(__dirname, '..', 'data', 'files.json');
    this.loadMetadata();

    const storage = multer.diskStorage({
      destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '..', 'uploads');
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
      },
      filename: (req, file, cb) => {
        const decodedName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        const ext = path.extname(decodedName);
        const baseName = path.basename(decodedName, ext);
        
        const uniqueName = `${baseName}_${Date.now()}${ext}`;
        const targetPath = path.join(__dirname, '..', 'uploads', uniqueName);
        
        cb(null, uniqueName);
      }
    });

    const fileFilter = (req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/quicktime'];
      if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('不支持的文件类型'));
      }
    };

    this.upload = multer({
      storage,
      fileFilter,
      limits: {
        fileSize: 100 * 1024 * 1024 // 100MB
      }
    });
  }

  private loadMetadata() {
    try {
      // 确保 data 目录存在
      const dataDir = path.dirname(this.metadataFile);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      // 如果元数据文件存在，则读取它
      if (fs.existsSync(this.metadataFile)) {
        const data = fs.readFileSync(this.metadataFile, 'utf8');
        this.uploadedFiles = JSON.parse(data);
      }
    } catch (error) {
      console.error('加载文件元数据失败:', error);
      this.uploadedFiles = [];
    }
  }

  private saveMetadata() {
    try {
      const dataDir = path.dirname(this.metadataFile);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      fs.writeFileSync(this.metadataFile, JSON.stringify(this.uploadedFiles, null, 2), 'utf8');
    } catch (error) {
      console.error('保存文件元数据失败:', error);
    }
  }

  public static getInstance(): FileService {
    if (!FileService.instance) {
      FileService.instance = new FileService();
    }
    return FileService.instance;
  }

  public getUploadMiddleware() {
    return this.upload.single('file');
  }

  public handleFileUpload(file: Express.Multer.File): { id: string; url: string } {
    const decodedName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    const fileId = path.parse(file.filename).name;
    const fileUrl = `/uploads/${file.filename}`;
    const fileType = file.mimetype.startsWith('video/') ? 'video' : 'image';

    const metadata: FileMetadata = {
      id: fileId,
      url: fileUrl,
      type: fileType,
      name: decodedName,
      uploadTime: new Date().toISOString()
    };

    this.uploadedFiles.push(metadata);
    this.saveMetadata();

    return {
      id: metadata.id,
      url: metadata.url
    };
  }

  public getUploadedFiles(): FileMetadata[] {
    return this.uploadedFiles;
  }

  public removeFile(fileId: string): boolean {
    const index = this.uploadedFiles.findIndex(file => file.id === fileId);
    if (index === -1) {
      return false;
    }

    const file = this.uploadedFiles[index];
    const filePath = path.join(__dirname, '..', 'uploads', path.basename(file.url));

    try {
      // 删除物理文件
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      // 从数组中移除
      this.uploadedFiles.splice(index, 1);
      // 保存更新后的元数据
      this.saveMetadata();
      return true;
    } catch (error) {
      console.error('删除文件失败:', error);
      return false;
    }
  }
} 