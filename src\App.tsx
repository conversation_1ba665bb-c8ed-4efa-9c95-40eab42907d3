/* eslint-disable @typescript-eslint/no-explicit-any */
// 参考文献 https://developer.mozilla.org/en-US/docs/Web/Media/Autoplay_guide
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import DigitalHuman from './components/DigitalHuman'
import ConfigPage from './components/ConfigPage'
import './App.css'

function App() {
    return (
        <Router>
            <Routes>
                <Route path="/" element={<DigitalHuman />} />
                <Route path="/config" element={<ConfigPage />} />
            </Routes>
        </Router>
    )
}

export default App
