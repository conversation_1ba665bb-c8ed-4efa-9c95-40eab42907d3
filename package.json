{"name": "realtime-digital-human-demo", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --mode development", "build": "tsc -b && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@bddh/starling-dhiframe": "^2.1.9", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/multer": "^1.4.12", "@types/react-resizable": "^3.0.8", "@types/react-router-dom": "^5.3.3", "antd": "^5.25.1", "axios": "^1.9.0", "cors": "^2.8.5", "express": "^5.1.0", "multer": "^1.4.5-lts.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-resizable": "^3.0.5", "react-router-dom": "^7.6.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "nodemon": "^3.1.10", "typescript": "^5.2.2", "vite": "^5.3.1"}}