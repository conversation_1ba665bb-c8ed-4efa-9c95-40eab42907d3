import express from 'express';
import path from 'path';
import cors from 'cors';
import axios from 'axios';
import multer from 'multer';
import http from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import { ConfigService } from './services/configService';
import { FileService } from './services/fileService';
import { AIService } from './services/aiService';
import { KnowledgeBaseService } from './services/knowledgeBaseService';
import { SpeechRecognitionService } from './services/speechRecognitionService';

const app = express();
const port = process.env.PORT || 3001;

// 创建HTTP服务器
const server = http.createServer(app);

// 创建WebSocket服务器
const wss = new WebSocketServer({ server });

// 启用 CORS
app.use(cors());
app.use(express.json());

// 配置服务接口
app.get('/api/config', async (req, res) => {
  try {
    const configService = ConfigService.getInstance();
    const config = await configService.getConfig();
    res.json(config);
  } catch (error) {
    console.error('获取配置失败:', error);
    res.status(500).json({ error: '获取配置失败' });
  }
});

app.post('/api/config', async (req, res) => {
  try {
    const configService = ConfigService.getInstance();
    await configService.saveConfig(req.body);
    res.json({ success: true });
  } catch (error) {
    console.error('保存配置失败:', error);
    res.status(500).json({ error: '保存配置失败' });
  }
});

// 上传文件接口
app.post('/api/upload-background', (req, res) => {
  const fileService = FileService.getInstance();
  fileService.getUploadMiddleware()(req, res, (err) => {
    if (err) {
      return res.status(400).json({ error: err.message });
    }
    
    try {
      if (!req.file) {
        return res.status(400).json({ error: '没有上传文件' });
      }

      const result = fileService.handleFileUpload(req.file);
      res.json(result);
    } catch (error) {
      console.error('文件上传失败:', error);
      res.status(500).json({ error: '文件上传失败' });
    }
  });
});

// 获取已上传文件列表接口
app.get('/api/backgrounds', (req, res) => {
  const fileService = FileService.getInstance();
  res.json(fileService.getUploadedFiles());
});

// 删除文件接口
app.delete('/api/backgrounds/:id', (req, res) => {
  const fileService = FileService.getInstance();
  const success = fileService.removeFile(req.params.id);
  if (success) {
    res.json({ success: true });
  } else {
    res.status(404).json({ error: '文件不存在' });
  }
});

// 提供静态文件访问
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// AI 聊天代理接口
app.post('/api/ai/chat', async (req, res) => {
  try {
    const aiService = AIService.getInstance();
    const { messages, userInput } = req.body;

    const result = await aiService.chat(messages || [], userInput);
    res.json(result);
  } catch (error) {
    console.error('AI聊天接口错误:', error);
    res.status(500).json({ 
      error: '服务器内部错误',
      choices: [{
        message: {
          content: '抱歉，我现在无法回答这个问题。'
        }
      }]
    });
  }
});

// AI 聊天流式响应接口 (SSE格式)
app.post('/api/ai/stream-chat', async (req, res) => {
  try {
    const aiService = AIService.getInstance();
    const { messages, userInput } = req.body;

    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    
    // 设置不超时
    req.socket.setTimeout(0);
    
    // 调用流式处理方法
    await aiService.streamChat(messages || [], userInput, res);
    
    // 结束响应
    res.end();
  } catch (error) {
    console.error('AI聊天流式接口错误:', error);
    
    // 确保已设置SSE头
    if (!res.headersSent) {
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
    }
    
    // 发送错误信息并结束响应
    res.write(`data: ${JSON.stringify({
      error: '服务器内部错误',
      choices: [{
        message: {
          content: '抱歉，我现在无法回答这个问题。'
        }
      }]
    })}\n\n`);
    res.write('data: [DONE]\n\n');
    res.end();
  }
});

// 知识库API代理接口
// 获取知识库列表
app.get('/api/knowledge-base/datasets', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const { page, limit } = req.query;
    const data = await knowledgeBaseService.getDatasets(
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined
    );
    res.json(data);
  } catch (error: any) {
    console.error('获取知识库列表失败:', error.message);
    res.status(500).json({ error: '获取知识库列表失败', details: error.message });
  }
});

// 创建知识库
app.post('/api/knowledge-base/datasets', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.createDataset(req.body);
    res.json(data);
  } catch (error: any) {
    console.error('创建知识库失败:', error);
    res.status(500).json({ error: '创建知识库失败' });
  }
});

// 删除知识库
app.delete('/api/knowledge-base/datasets/:datasetId', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const status = await knowledgeBaseService.deleteDataset(req.params.datasetId);
    res.status(status).end();
  } catch (error: any) {
    console.error('删除知识库失败:', error);
    res.status(500).json({ error: '删除知识库失败' });
  }
});

// 获取文档列表
app.get('/api/knowledge-base/datasets/:datasetId/documents', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const { page, limit } = req.query;
    const data = await knowledgeBaseService.getDocuments(
      req.params.datasetId,
      page ? Number(page) : undefined,
      limit ? Number(limit) : undefined
    );
    res.json(data);
  } catch (error: any) {
    console.error('获取文档列表失败:', error);
    res.status(500).json({ error: '获取文档列表失败' });
  }
});

// 通过文本创建文档
app.post('/api/knowledge-base/datasets/:datasetId/document/create_by_text', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.createDocumentByText(req.params.datasetId, req.body);
    res.json(data);
  } catch (error: any) {
    console.error('创建文档失败:', error);
    res.status(500).json({ error: '创建文档失败' });
  }
});

// 通过文件创建文档
const upload = multer({ storage: multer.memoryStorage() });
app.post('/api/knowledge-base/datasets/:datasetId/document/create_by_file', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: '没有上传文件' });
    }

    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.createDocumentByFile(
      req.params.datasetId,
      req.file,
      req.body.data
    );
    res.json(data);
  } catch (error: any) {
    console.error('上传文件失败:', error);
    res.status(500).json({ error: '上传文件失败' });
  }
});

// 删除文档
app.delete('/api/knowledge-base/datasets/:datasetId/documents/:documentId', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.deleteDocument(
      req.params.datasetId,
      req.params.documentId
    );
    res.json(data);
  } catch (error: any) {
    console.error('删除文档失败:', error);
    res.status(500).json({ error: '删除文档失败' });
  }
});

// 获取文档分段
app.get('/api/knowledge-base/datasets/:datasetId/documents/:documentId/segments', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.getSegments(
      req.params.datasetId,
      req.params.documentId
    );
    res.json(data);
  } catch (error: any) {
    console.error('获取文档分段失败:', error);
    res.status(500).json({ error: '获取文档分段失败' });
  }
});

// 添加分段
app.post('/api/knowledge-base/datasets/:datasetId/documents/:documentId/segments', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.createSegment(
      req.params.datasetId,
      req.params.documentId,
      req.body
    );
    res.json(data);
  } catch (error: any) {
    console.error('添加分段失败:', error);
    res.status(500).json({ error: '添加分段失败' });
  }
});

// 删除分段
app.delete('/api/knowledge-base/datasets/:datasetId/documents/:documentId/segments/:segmentId', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.deleteSegment(
      req.params.datasetId,
      req.params.documentId,
      req.params.segmentId
    );
    res.json(data);
  } catch (error: any) {
    console.error('删除分段失败:', error);
    res.status(500).json({ error: '删除分段失败' });
  }
});

// 更新分段
app.post('/api/knowledge-base/datasets/:datasetId/documents/:documentId/segments/:segmentId', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.updateSegment(
      req.params.datasetId,
      req.params.documentId,
      req.params.segmentId,
      req.body
    );
    res.json(data);
  } catch (error: any) {
    console.error('更新分段失败:', error);
    res.status(500).json({ error: '更新分段失败' });
  }
});

// 获取文档嵌入状态（进度）
app.get('/api/knowledge-base/datasets/:datasetId/documents/:batch/indexing-status', async (req, res) => {
  try {
    const knowledgeBaseService = KnowledgeBaseService.getInstance();
    const data = await knowledgeBaseService.getIndexingStatus(
      req.params.datasetId,
      req.params.batch
    );
    res.json(data);
  } catch (error: any) {
    console.error('获取文档嵌入状态失败:', error);
    res.status(500).json({ error: '获取文档嵌入状态失败' });
  }
});

// WebSocket连接处理
wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
  const ip = req.socket.remoteAddress;
  const url = new URL(req.url || '', `http://${req.headers.host}`);
  const pathname = url.pathname;
  
  console.log(`WebSocket客户端已连接 - IP: ${ip}, 路径: ${pathname}`);
  
  // 检查连接路径
  if (pathname === '/speech-recognition') {
    // 语音识别WebSocket
    console.log('创建语音识别会话');
    const speechRecognitionService = SpeechRecognitionService.getInstance();
    speechRecognitionService.createSession(ws);
  } else {
    // 未知的WebSocket路径
    console.warn(`未知的WebSocket路径: ${pathname}`);
    ws.close(1000, '未知的WebSocket路径');
  }
});

// 启动服务器
server.listen(port, () => {
  console.log(`服务器运行在 http://localhost:${port}`);
}); 