import axios from 'axios';
import { API_BASE_URL, STATIC_BASE_URL } from '../config/constants';

export interface UploadedBackground {
  id: string;
  url: string;
  type: 'image' | 'video';
  name: string;
}

export class UploadService {
  private static instance: UploadService;

  private constructor() {}

  public static getInstance(): UploadService {
    if (!UploadService.instance) {
      UploadService.instance = new UploadService();
    }
    return UploadService.instance;
  }

  public async uploadBackground(file: File): Promise<{ id: string; url: string }> {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await axios.post(`${API_BASE_URL}/upload-background`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // 检查响应数据
      if (!response.data || !response.data.url) {
        throw new Error('服务器返回的数据格式不正确');
      }

      // 处理URL路径
      let url = response.data.url;
      if (!url.startsWith('http')) {
        // 如果URL以/uploads开头，直接使用静态资源基础URL
        if (url.startsWith('/uploads/')) {
          url = `${STATIC_BASE_URL}${url}`;
        } else {
          // 否则使用API基础URL
          url = `${API_BASE_URL}${url}`;
        }
      }

      return {
        id: response.data.id,
        url: url
      };
    } catch (error) {
      console.error('上传背景失败:', error);
      throw new Error('上传背景失败');
    }
  }

  public async getUploadedBackgrounds(): Promise<UploadedBackground[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/backgrounds`);
      
      // 确保返回的数据格式正确
      if (!Array.isArray(response.data)) {
        throw new Error('服务器返回的数据格式不正确');
      }

      // 处理每个背景的URL
      return response.data.map(item => {
        let url = item.url;
        if (!url.startsWith('http')) {
          // 如果URL以/uploads开头，直接使用静态资源基础URL
          if (url.startsWith('/uploads/')) {
            url = `${STATIC_BASE_URL}${url}`;
          } else {
            // 否则使用API基础URL
            url = `${API_BASE_URL}${url}`;
          }
        }
        return {
          ...item,
          url: url
        };
      });
    } catch (error) {
      console.error('获取背景列表失败:', error);
      throw new Error('获取背景列表失败');
    }
  }

  public async deleteBackground(id: string): Promise<void> {
    try {
      await axios.delete(`${API_BASE_URL}/backgrounds/${id}`);
    } catch (error) {
      console.error('删除背景失败:', error);
      throw new Error('删除背景失败');
    }
  }
} 